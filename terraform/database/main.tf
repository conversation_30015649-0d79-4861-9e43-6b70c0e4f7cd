terraform {
  required_providers {
    digitalocean = {
      source  = "digitalocean/digitalocean"
      version = "~> 2.0"
    }
    http = {
      source  = "hashicorp/http"
      version = "~> 3.0"
    }
    local = {
      source  = "hashicorp/local"
      version = "~> 2.0"
    }
  }
}

provider "digitalocean" {
  token = var.do_token
}

data "digitalocean_database_cluster" "example" {
  name = "db-ops-admin"
}

# HTTP data source to get all connection pools for the database cluster
data "http" "database_connection_pools" {
  url = "https://api.digitalocean.com/v2/databases/${data.digitalocean_database_cluster.example.id}/pools"

  request_headers = {
    Authorization = "Bearer ${var.do_token}"
    Content-Type  = "application/json"
  }
}

# Parse the JSON response to extract connection pools
locals {
  connection_pools_response = jsondecode(data.http.database_connection_pools.response_body)
  connection_pools = local.connection_pools_response.pools
}

# Output the connection pools information
output "connection_pools" {
  description = "All connection pools in the database cluster"
  value = {
    pools = local.connection_pools
    total_count = length(local.connection_pools)
  }
}

# Output individual pool details for easier access
output "connection_pool_names" {
  description = "List of connection pool names"
  value = [for pool in local.connection_pools : pool.name]
}

output "connection_pool_details" {
  description = "Detailed information about each connection pool"
  value = {
    for pool in local.connection_pools : pool.name => {
      mode = pool.mode
      size = pool.size
      db   = pool.db
      user = pool.user
      connection = pool.connection
    }
  }
}


